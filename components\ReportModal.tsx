
import React from 'react';
import type { Report } from '../types';
import * as Icons from './Icons';

interface ReportModalProps {
    report: Report;
    onClose: () => void;
}

const ReportModal: React.FC<ReportModalProps> = ({ report, onClose }) => {
    // A type assertion to treat Icons as an indexable object
    const IconComponents = Icons as { [key: string]: React.FC<any> };
    const PreviewComponent = IconComponents[report.previewComponent] || Icons.GenericReportPreview;

    return (
        <div 
            className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-50 p-4"
            onClick={onClose}
            role="dialog"
            aria-modal="true"
            aria-labelledby="report-title"
        >
            <div 
                className="bg-slate-800 rounded-lg shadow-2xl p-4 w-full max-w-lg border border-slate-600 relative"
                onClick={(e) => e.stopPropagation()}
            >
                <div className="flex justify-between items-center mb-3 pb-2 border-b border-slate-700">
                    <h2 id="report-title" className="text-lg font-semibold text-white">{report.title}</h2>
                    <button 
                        onClick={onClose} 
                        className="text-slate-400 hover:text-white transition-colors rounded-full p-1"
                        aria-label="Close report preview"
                    >
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6">
                          <path strokeLinecap="round" strokeLinejoin="round" d="M6 18 18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
                <div className="bg-slate-700 p-4 rounded-md">
                     <div className="bg-white p-2 rounded-sm shadow-inner">
                        <PreviewComponent />
                     </div>
                </div>
                 <div className="text-center mt-2 text-sm text-slate-500">
                    2 pages
                </div>
            </div>
        </div>
    );
};

export default ReportModal;
