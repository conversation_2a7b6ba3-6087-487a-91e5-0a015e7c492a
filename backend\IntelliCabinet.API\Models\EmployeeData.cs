using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IntelliCabinet.API.Models
{
    public class EmployeeData
    {
        [Key]
        public string Id { get; set; } = string.Empty;

        [Required]
        public string EmployeeId { get; set; } = string.Empty;

        // Required Fields
        [Required]
        [Column(TypeName = "date")]
        public DateTime Date { get; set; }

        [Required]
        public int Pages { get; set; }

        [Required]
        [MaxLength(10)]
        public string Office { get; set; } = string.Empty; // 'OPP' | 'OCSPP' | 'OW' | ''

        [Required]
        [MaxLength(100)]
        public string DocketId { get; set; } = string.Empty;

        [Required]
        [MaxLength(50)]
        public string DocNumber { get; set; } = string.Empty;

        // Work/Doc/Format - stored as JSON strings
        [Required]
        public string WorkDone { get; set; } = "[]"; // JSON array of WorkType

        [Required]
        [MaxLength(20)]
        public string DocumentType { get; set; } = string.Empty; // 'comment' | 'support' | ''

        [Required]
        public string Format { get; set; } = "[]"; // JSON array of FormatType

        // Optional Fields
        [Required]
        [MaxLength(20)]
        public string IsLate { get; set; } = "on_time"; // 'on_time' | 'late'

        public string Notes { get; set; } = string.Empty;

        public string QaMetadataErrors { get; set; } = string.Empty;

        public string QaIndexerName { get; set; } = string.Empty;

        // Navigation properties
        [ForeignKey("EmployeeId")]
        public virtual User Employee { get; set; } = null!;
    }
}
