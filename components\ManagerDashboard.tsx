
import React, { useState, useContext } from 'react';
import { AppContext } from '../context/AppContext';
import MainMenu from './MainMenu';
import MonthlyReports from './MonthlyReports';
import ReportModal from './ReportModal';
import type { Report } from '../types';

const ManagerDashboard: React.FC = () => {
    const { currentUser, setCurrentUser, setActiveItem } = useContext(AppContext);
    const [view, setView] = useState<'main' | 'monthly_reports'>('main');
    const [selectedReport, setSelectedReport] = useState<Report | null>(null);

    const handleLogout = () => {
        setActiveItem(null);
        setCurrentUser(null);
    };

    if (!currentUser) return null;

    const renderView = () => {
        switch (view) {
            case 'monthly_reports':
                return <MonthlyReports onSelectReport={setSelectedReport} onBack={() => setView('main')} />;
            case 'main':
            default:
                return <MainMenu user={currentUser} onNavigate={setView} onLogout={handleLogout} />;
        }
    };
    
    const getHeaderTitle = () => {
        switch (view) {
            case 'monthly_reports': return 'Monthly Reports';
            case 'main': default: return 'MetaData/QA Main Menu';
        }
    };

    return (
        <div className="h-screen w-screen flex flex-col bg-gray-400 font-sans">
            <header className="bg-gray-500 p-2 border-b-2 border-gray-700 shadow-md">
                <h1 className="text-black text-center font-semibold tracking-wider">{getHeaderTitle()}</h1>
            </header>
            <main className="flex-1 flex items-center justify-center p-4 md:p-8 overflow-y-auto">
                 {renderView()}
            </main>
            {selectedReport && (
                <ReportModal report={selectedReport} onClose={() => setSelectedReport(null)} />
            )}
        </div>
    );
};

export default ManagerDashboard;
