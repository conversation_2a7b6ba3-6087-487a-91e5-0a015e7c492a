using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using IntelliCabinet.API.Data;
using IntelliCabinet.API.DTOs;
using System.Text.Json;

namespace IntelliCabinet.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class ReportsController : ControllerBase
    {
        private readonly IntelliCabinetDbContext _context;

        public ReportsController(IntelliCabinetDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<ReportDto>>> GetReports()
        {
            var reports = await _context.Reports.ToListAsync();

            var reportDtos = reports.Select(r => new ReportDto
            {
                Id = r.Id,
                Title = r.Title,
                Data = JsonSerializer.Deserialize<object[]>(r.Data) ?? Array.Empty<object>(),
                Category = r.Category,
                PreviewComponent = r.PreviewComponent
            }).ToList();

            return Ok(reportDtos);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<ReportDto>> GetReport(string id)
        {
            var report = await _context.Reports
                .Where(r => r.Id == id)
                .FirstOrDefaultAsync();

            if (report == null)
            {
                return NotFound();
            }

            var dto = new ReportDto
            {
                Id = report.Id,
                Title = report.Title,
                Data = JsonSerializer.Deserialize<object[]>(report.Data) ?? Array.Empty<object>(),
                Category = report.Category,
                PreviewComponent = report.PreviewComponent
            };

            return Ok(dto);
        }
    }
}
