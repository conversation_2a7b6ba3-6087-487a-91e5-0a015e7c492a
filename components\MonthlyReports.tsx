
import React, { useState } from 'react';
import { useDatabase } from '../hooks/useDatabase';
import type { Report } from '../types';

interface MonthlyReportsProps {
    onSelectReport: (report: Report) => void;
    onBack: () => void;
}

const ReportSection: React.FC<{title: string, children: React.ReactNode}> = ({ title, children }) => (
    <div className="border-2 border-black p-2 pt-0 shadow-md">
        <div className="bg-orange-200 border-2 border-black -mx-2 -mt-0 px-2 font-bold text-black mb-2 shadow-sm">
            {title}
        </div>
        <div className="space-y-2">
            {children}
        </div>
    </div>
);

const ReportButton: React.FC<{label: string, onClick: () => void}> = ({ label, onClick }) => (
    <button 
        onClick={onClick} 
        className="w-full text-left bg-stone-100 p-2 border-2 border-t-white border-l-white border-r-black border-b-black hover:bg-stone-200 active:border-t-black active:border-l-black active:border-r-white active:border-b-white focus:outline-none focus:ring-2 focus:ring-blue-500 font-semibold text-black text-sm"
    >
        {label}
    </button>
);


const MonthlyReports: React.FC<MonthlyReportsProps> = ({ onSelectReport, onBack }) => {
    const { getReports } = useDatabase();
    const [startDate, setStartDate] = useState('2025-06-01');
    const [endDate, setEndDate] = useState('2025-06-16');

    const reports = getReports();

    const renderReportButtons = (category: Report['category']) => {
        return reports
            .filter(r => r.category === category)
            .map(report => (
                <ReportButton key={report.id} label={report.title} onClick={() => onSelectReport(report)} />
            ));
    };

    return (
        <div className="bg-stone-200 p-4 border-2 border-t-white border-l-white border-r-black border-b-black w-full max-w-4xl relative shadow-lg">
            <button onClick={onBack} className="absolute top-2 left-2 text-sm font-bold text-blue-700 hover:underline">
                &lt; Main Menu
            </button>
            <h2 className="text-center text-xl font-bold bg-orange-200 border-2 border-t-black border-l-black border-r-gray-500 border-b-gray-500 inline-block px-6 py-1 shadow-md mb-4 ml-1/2 -translate-x-1/2">
                Monthly Reports
            </h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-1 space-y-4">
                    <div className="border-2 border-black p-2 pt-0">
                         <div className="bg-orange-200 border-2 border-black -mx-2 -mt-0 px-2 font-bold text-black mb-2">
                            Date Range
                        </div>
                        <div className="space-y-2">
                            <div>
                                <label className="block text-sm font-semibold text-black">Start Date</label>
                                <input type="date" value={startDate} onChange={e => setStartDate(e.target.value)} className="w-full p-1 border-2 border-black inset-shadow" />
                            </div>
                             <div>
                                <label className="block text-sm font-semibold text-black">End Date</label>
                                <input type="date" value={endDate} onChange={e => setEndDate(e.target.value)} className="w-full p-1 border-2 border-black inset-shadow" />
                            </div>
                        </div>
                    </div>
                    <div className="bg-blue-200 border-2 border-black p-2 text-sm text-black">
                        The reports below are to assist in completing the monthly reports. Enter a date range and click on the desired report.
                    </div>
                </div>

                <div className="md:col-span-2 grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="space-y-4">
                        <ReportSection title="Spreadsheet Reports">
                            {renderReportButtons('Spreadsheet')}
                        </ReportSection>
                        <ReportSection title="Productivity Reports">
                            {renderReportButtons('Productivity')}
                        </ReportSection>
                    </div>
                     <ReportSection title="Narrative Reports">
                        {renderReportButtons('Narrative')}
                    </ReportSection>
                </div>
            </div>
        </div>
    );
};

export default MonthlyReports;
