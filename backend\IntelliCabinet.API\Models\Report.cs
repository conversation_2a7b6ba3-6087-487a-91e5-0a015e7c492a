using System.ComponentModel.DataAnnotations;

namespace IntelliCabinet.API.Models
{
    public class Report
    {
        [Key]
        public string Id { get; set; } = string.Empty;

        [Required]
        [MaxLength(200)]
        public string Title { get; set; } = string.Empty;

        [Required]
        public string Data { get; set; } = "[]"; // JSON array of report data

        [MaxLength(50)]
        public string? Category { get; set; } // 'Spreadsheet' | 'Productivity' | 'Narrative'

        [MaxLength(100)]
        public string? PreviewComponent { get; set; }
    }
}
