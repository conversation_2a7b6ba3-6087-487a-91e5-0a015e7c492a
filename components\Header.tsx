
import React, { useContext } from 'react';
import { AppContext } from '../context/AppContext';
import { LogoutIcon, UserIcon } from './Icons';

const Header: React.FC = () => {
    const { currentUser, setCurrentUser, setActiveItem } = useContext(AppContext);

    const handleLogout = () => {
        setActiveItem(null);
        setCurrentUser(null);
    };

    if (!currentUser) return null;

    return (
        <header className="bg-slate-900/70 backdrop-blur-sm border-b border-slate-700/50">
            <div className="flex items-center justify-between h-16 px-6">
                <div className="text-xl font-bold tracking-wider text-slate-200">
                    Intelli<span className="text-blue-400">Cabinet</span>
                </div>
                <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                         <UserIcon className="h-6 w-6 text-slate-400" />
                        <span className="text-slate-300 font-medium">{currentUser.name}</span>
                        <span className="text-xs font-semibold px-2 py-0.5 rounded-full bg-slate-700 text-slate-300 capitalize">
                            {currentUser.role}
                        </span>
                    </div>
                    <button
                        onClick={handleLogout}
                        className="p-2 rounded-full hover:bg-slate-700 transition-colors duration-200"
                        aria-label="Logout"
                    >
                        <LogoutIcon className="h-6 w-6 text-slate-400" />
                    </button>
                </div>
            </div>
        </header>
    );
};

export default Header;
