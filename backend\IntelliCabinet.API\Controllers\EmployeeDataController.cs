using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using IntelliCabinet.API.Data;
using IntelliCabinet.API.DTOs;
using IntelliCabinet.API.Models;
using IntelliCabinet.API.Attributes;
using System.Text.Json;

namespace IntelliCabinet.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    // [RequireAuth] // Uncomment when Auth0 is fully configured
    public class EmployeeDataController : ControllerBase
    {
        private readonly IntelliCabinetDbContext _context;

        public EmployeeDataController(IntelliCabinetDbContext context)
        {
            _context = context;
        }

        [HttpGet("employee/{employeeId}")]
        public async Task<ActionResult<EmployeeDataDto>> GetEmployeeData(string employeeId)
        {
            var employeeData = await _context.EmployeeData
                .Where(ed => ed.EmployeeId == employeeId)
                .FirstOrDefaultAsync();

            if (employeeData == null)
            {
                // Return a new empty record for the employee
                return Ok(new EmployeeDataDto
                {
                    Id = $"new-{employeeId}",
                    EmployeeId = employeeId,
                    Date = "",
                    Pages = 0,
                    Office = "",
                    DocketId = "",
                    DocNumber = "",
                    WorkDone = Array.Empty<string>(),
                    DocumentType = "",
                    Format = Array.Empty<string>(),
                    IsLate = "on_time",
                    Notes = "",
                    QaMetadataErrors = "",
                    QaIndexerName = ""
                });
            }

            var dto = new EmployeeDataDto
            {
                Id = employeeData.Id,
                EmployeeId = employeeData.EmployeeId,
                Date = employeeData.Date.ToString("yyyy-MM-dd"),
                Pages = employeeData.Pages,
                Office = employeeData.Office,
                DocketId = employeeData.DocketId,
                DocNumber = employeeData.DocNumber,
                WorkDone = JsonSerializer.Deserialize<string[]>(employeeData.WorkDone) ?? Array.Empty<string>(),
                DocumentType = employeeData.DocumentType,
                Format = JsonSerializer.Deserialize<string[]>(employeeData.Format) ?? Array.Empty<string>(),
                IsLate = employeeData.IsLate,
                Notes = employeeData.Notes,
                QaMetadataErrors = employeeData.QaMetadataErrors,
                QaIndexerName = employeeData.QaIndexerName
            };

            return Ok(dto);
        }

        [HttpPost]
        public async Task<ActionResult<EmployeeDataDto>> CreateEmployeeData(CreateEmployeeDataDto createDto)
        {
            var employeeData = new EmployeeData
            {
                Id = Guid.NewGuid().ToString(),
                EmployeeId = createDto.EmployeeId,
                Date = DateTime.Parse(createDto.Date),
                Pages = createDto.Pages,
                Office = createDto.Office,
                DocketId = createDto.DocketId,
                DocNumber = createDto.DocNumber,
                WorkDone = JsonSerializer.Serialize(createDto.WorkDone),
                DocumentType = createDto.DocumentType,
                Format = JsonSerializer.Serialize(createDto.Format),
                IsLate = createDto.IsLate,
                Notes = createDto.Notes,
                QaMetadataErrors = createDto.QaMetadataErrors,
                QaIndexerName = createDto.QaIndexerName
            };

            _context.EmployeeData.Add(employeeData);
            await _context.SaveChangesAsync();

            var dto = new EmployeeDataDto
            {
                Id = employeeData.Id,
                EmployeeId = employeeData.EmployeeId,
                Date = employeeData.Date.ToString("yyyy-MM-dd"),
                Pages = employeeData.Pages,
                Office = employeeData.Office,
                DocketId = employeeData.DocketId,
                DocNumber = employeeData.DocNumber,
                WorkDone = createDto.WorkDone,
                DocumentType = employeeData.DocumentType,
                Format = createDto.Format,
                IsLate = employeeData.IsLate,
                Notes = employeeData.Notes,
                QaMetadataErrors = employeeData.QaMetadataErrors,
                QaIndexerName = employeeData.QaIndexerName
            };

            return CreatedAtAction(nameof(GetEmployeeData), new { employeeId = employeeData.EmployeeId }, dto);
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> UpdateEmployeeData(string id, UpdateEmployeeDataDto updateDto)
        {
            var employeeData = await _context.EmployeeData.FindAsync(id);
            if (employeeData == null)
            {
                return NotFound();
            }

            employeeData.Date = DateTime.Parse(updateDto.Date);
            employeeData.Pages = updateDto.Pages;
            employeeData.Office = updateDto.Office;
            employeeData.DocketId = updateDto.DocketId;
            employeeData.DocNumber = updateDto.DocNumber;
            employeeData.WorkDone = JsonSerializer.Serialize(updateDto.WorkDone);
            employeeData.DocumentType = updateDto.DocumentType;
            employeeData.Format = JsonSerializer.Serialize(updateDto.Format);
            employeeData.IsLate = updateDto.IsLate;
            employeeData.Notes = updateDto.Notes;
            employeeData.QaMetadataErrors = updateDto.QaMetadataErrors;
            employeeData.QaIndexerName = updateDto.QaIndexerName;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!EmployeeDataExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            return NoContent();
        }

        private bool EmployeeDataExists(string id)
        {
            return _context.EmployeeData.Any(e => e.Id == id);
        }
    }
}
