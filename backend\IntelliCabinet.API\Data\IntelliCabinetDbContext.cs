using Microsoft.EntityFrameworkCore;
using IntelliCabinet.API.Models;

namespace IntelliCabinet.API.Data
{
    public class IntelliCabinetDbContext : DbContext
    {
        public IntelliCabinetDbContext(DbContextOptions<IntelliCabinetDbContext> options)
            : base(options)
        {
        }

        public DbSet<User> Users { get; set; }
        public DbSet<Department> Departments { get; set; }
        public DbSet<EmployeeData> EmployeeData { get; set; }
        public DbSet<Report> Reports { get; set; }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            // Configure relationships
            modelBuilder.Entity<User>()
                .HasOne(u => u.Department)
                .WithMany(d => d.Users)
                .HasForeignKey(u => u.DepartmentId)
                .OnDelete(DeleteBehavior.Restrict);

            modelBuilder.Entity<EmployeeData>()
                .HasOne(ed => ed.Employee)
                .WithMany(u => u.EmployeeDataRecords)
                .HasForeignKey(ed => ed.EmployeeId)
                .OnDelete(DeleteBehavior.Cascade);

            // Seed data
            SeedData(modelBuilder);
        }

        private void SeedData(ModelBuilder modelBuilder)
        {
            // Seed Departments
            modelBuilder.Entity<Department>().HasData(
                new Department { Id = "d_meta", Name = "Metadata" },
                new Department { Id = "d_qa", Name = "Quality Assurance" }
            );

            // Seed Users
            modelBuilder.Entity<User>().HasData(
                new User { Id = "u_alice", Name = "Alice Smith", Role = "employee", RoleLabel = "Metadata Specialist", DepartmentId = "d_meta" },
                new User { Id = "u_charlie", Name = "Charlie Brown", Role = "employee", RoleLabel = "Metadata Specialist", DepartmentId = "d_meta" },
                new User { Id = "u_diana", Name = "Diana Prince", Role = "employee", RoleLabel = "QA Specialist", DepartmentId = "d_qa" },
                new User { Id = "u_ethan", Name = "Ethan Hunt", Role = "employee", RoleLabel = "QA Specialist", DepartmentId = "d_qa" },
                new User { Id = "u_timothy", Name = "Timothy Young", Role = "manager", RoleLabel = "Manager", DepartmentId = "d_qa" }
            );

            // Seed EmployeeData
            modelBuilder.Entity<EmployeeData>().HasData(
                new EmployeeData
                {
                    Id = "ed_alice",
                    EmployeeId = "u_alice",
                    Date = new DateTime(2025, 6, 16),
                    Pages = 5,
                    Office = "OPP",
                    DocketId = "EPA-HQ-OPP-2021-0221",
                    DocNumber = "0123",
                    WorkDone = "[\"metadata\"]",
                    DocumentType = "comment",
                    Format = "[\"fdms\"]",
                    IsLate = "on_time",
                    Notes = "Standard processing.",
                    QaMetadataErrors = "",
                    QaIndexerName = ""
                },
                new EmployeeData
                {
                    Id = "ed_charlie",
                    EmployeeId = "u_charlie",
                    Date = new DateTime(2025, 6, 15),
                    Pages = 2,
                    Office = "OCSPP",
                    DocketId = "EPA-HQ-OCSPP-2020-0585",
                    DocNumber = "0054",
                    WorkDone = "[\"metadata\"]",
                    DocumentType = "support",
                    Format = "[\"email\", \"hardcopy\"]",
                    IsLate = "on_time",
                    Notes = "",
                    QaMetadataErrors = "",
                    QaIndexerName = ""
                },
                new EmployeeData
                {
                    Id = "ed_diana",
                    EmployeeId = "u_diana",
                    Date = new DateTime(2025, 6, 14),
                    Pages = 12,
                    Office = "OW",
                    DocketId = "EPA-HQ-OW-2019-0372",
                    DocNumber = "0101",
                    WorkDone = "[\"qa\"]",
                    DocumentType = "comment",
                    Format = "[\"fdms\"]",
                    IsLate = "late",
                    Notes = "Received after deadline.",
                    QaMetadataErrors = "2 minor errors found in abstract.",
                    QaIndexerName = "Alice Smith"
                },
                new EmployeeData
                {
                    Id = "ed_ethan",
                    EmployeeId = "u_ethan",
                    Date = new DateTime(2025, 6, 13),
                    Pages = 50,
                    Office = "OPP",
                    DocketId = "EPA-HQ-OPP-2021-0221",
                    DocNumber = "0119",
                    WorkDone = "[\"qa\"]",
                    DocumentType = "support",
                    Format = "[\"other_media\"]",
                    IsLate = "on_time",
                    Notes = "Large submission review.",
                    QaMetadataErrors = "No errors.",
                    QaIndexerName = "Charlie Brown"
                }
            );

            // Seed Reports
            modelBuilder.Entity<Report>().HasData(
                new Report { Id = "r_legacy", Title = "Legacy Report", Data = "[{\"Status\": \"Complete\", \"Date\": \"2024-07-10\", \"Items Archived\": 1204}]", Category = "Narrative", PreviewComponent = "LegacyReportPreview" },
                new Report { Id = "r_weekly", Title = "Weekly Report", Data = "[{\"Week\": \"28\", \"ItemsProcessed\": 450, \"Pending\": 32}]", Category = "Productivity", PreviewComponent = "WeeklyReportPreview" },
                new Report { Id = "r_planner", Title = "Planner Report", Data = "[{\"Task\": \"Review Submissions Q3\", \"Due\": \"2024-07-15\", \"Assignee\": \"All\"}]", Category = "Productivity", PreviewComponent = "PlannerReportPreview" },
                new Report { Id = "r_erule", Title = "eRule Report", Data = "[{\"RuleId\": \"ER-2024-05\", \"Status\": \"Draft\", \"Last Update\": \"2024-07-12\"}]", Category = "Spreadsheet", PreviewComponent = "ERuleReportPreview" },
                new Report { Id = "r_oar_fr", Title = "OAR FR Report", Data = "[{\"FR_Number\": \"FR-12345\", \"PublicationDate\": \"2024-07-20\", \"Docket ID\": \"EPA-HQ-OAR-2023-0112\"}]", Category = "Spreadsheet", PreviewComponent = "OarFrReportPreview" },
                new Report { Id = "r_oar_tracking", Title = "OAR Tracking", Data = "[{\"OAR_ID\": \"OAR-54321\", \"Step\": \"Final Review\", \"Next Step\": \"Publication\"}]", Category = "Spreadsheet", PreviewComponent = "OarTrackingReportPreview" },
                new Report { Id = "r_ow_tracking", Title = "OW Tracking", Data = "[{\"OW_ID\": \"OW-98765\", \"Status\": \"In Progress\", \"Lead\": \"D. Prince\"}]", Category = "Spreadsheet", PreviewComponent = "OwTrackingReportPreview" }
            );
        }
    }
}
