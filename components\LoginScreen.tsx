
import React, { useContext } from 'react';
import { AppContext } from '../context/AppContext';
import { useDatabase } from '../hooks/useDatabase';
import type { User } from '../types';

const LoginScreen: React.FC = () => {
    const { setCurrentUser } = useContext(AppContext);
    const { getUsers } = useDatabase();
    const users = getUsers();

    const handleLogin = (user: User) => {
        setCurrentUser(user);
    };

    return (
        <div className="min-h-screen flex items-center justify-center bg-slate-900 p-4">
            <div className="w-full max-w-md bg-slate-800 rounded-2xl shadow-2xl p-8 border border-slate-700">
                <h1 className="text-3xl font-bold text-center text-white mb-2">IntelliCabinet</h1>
                <p className="text-center text-slate-400 mb-8">Select a user to log in</p>
                <div className="space-y-4">
                    {users.map((user) => (
                        <button
                            key={user.id}
                            onClick={() => handleLogin(user)}
                            className="w-full flex items-center justify-between p-4 bg-slate-700/50 hover:bg-slate-700 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-slate-800"
                        >
                            <div>
                                <p className="font-semibold text-lg text-white">{user.name}</p>
                                <p className="text-sm text-slate-400 capitalize">{user.roleLabel}</p>
                            </div>
                            <span className="text-sm font-medium px-3 py-1 rounded-full bg-blue-600/50 text-blue-200">
                                Login
                            </span>
                        </button>
                    ))}
                </div>
            </div>
        </div>
    );
};

export default LoginScreen;
