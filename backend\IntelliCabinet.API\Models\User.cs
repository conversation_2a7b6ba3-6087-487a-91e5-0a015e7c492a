using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace IntelliCabinet.API.Models
{
    public class User
    {
        [Key]
        public string Id { get; set; } = string.Empty;

        [Required]
        [MaxLength(100)]
        public string Name { get; set; } = string.Empty;

        [Required]
        [MaxLength(20)]
        public string Role { get; set; } = string.Empty; // 'employee' | 'manager'

        [Required]
        [MaxLength(50)]
        public string RoleLabel { get; set; } = string.Empty;

        [Required]
        public string DepartmentId { get; set; } = string.Empty;

        // Navigation properties
        [ForeignKey("DepartmentId")]
        public virtual Department Department { get; set; } = null!;

        public virtual ICollection<EmployeeData> EmployeeDataRecords { get; set; } = new List<EmployeeData>();
    }
}
