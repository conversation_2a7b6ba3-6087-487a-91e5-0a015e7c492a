import { useMemo } from 'react';
import type { User, Department, Report, EmployeeData } from '../types';

const departments: Department[] = [
    { id: 'd_meta', name: 'Metada<PERSON>' },
    { id: 'd_qa', name: 'Quality Assurance' },
];

const users: User[] = [
    { id: 'u_alice', name: '<PERSON>', role: 'employee', roleLabel: 'Metadata Specialist', departmentId: 'd_meta' },
    { id: 'u_charlie', name: '<PERSON>', role: 'employee', roleLabel: 'Metadata Specialist', departmentId: 'd_meta' },
    { id: 'u_diana', name: '<PERSON>', role: 'employee', roleLabel: 'QA Specialist', departmentId: 'd_qa' },
    { id: 'u_ethan', name: '<PERSON>', role: 'employee', roleLabel: 'QA Specialist', departmentId: 'd_qa' },
    { id: 'u_timothy', name: '<PERSON>', role: 'manager', roleLabel: 'Manager', departmentId: 'd_qa' },
];

const employeeData: EmployeeData[] = [
    { // <PERSON>
        id: 'ed_alice', employeeId: 'u_alice',
        date: '2025-06-16', pages: 5, office: 'OPP', docketId: 'EPA-HQ-OPP-2021-0221', docNumber: '0123',
        workDone: ['metadata'], documentType: 'comment', format: ['fdms'],
        isLate: 'on_time', notes: 'Standard processing.', qaMetadataErrors: '', qaIndexerName: '',
    },
    { // Charlie Brown
        id: 'ed_charlie', employeeId: 'u_charlie',
        date: '2025-06-15', pages: 2, office: 'OCSPP', docketId: 'EPA-HQ-OCSPP-2020-0585', docNumber: '0054',
        workDone: ['metadata'], documentType: 'support', format: ['email', 'hardcopy'],
        isLate: 'on_time', notes: '', qaMetadataErrors: '', qaIndexerName: '',
    },
    { // Diana Prince
        id: 'ed_diana', employeeId: 'u_diana',
        date: '2025-06-14', pages: 12, office: 'OW', docketId: 'EPA-HQ-OW-2019-0372', docNumber: '0101',
        workDone: ['qa'], documentType: 'comment', format: ['fdms'],
        isLate: 'late', notes: 'Received after deadline.', qaMetadataErrors: '2 minor errors found in abstract.', qaIndexerName: 'Alice Smith',
    },
    { // Ethan Hunt
        id: 'ed_ethan', employeeId: 'u_ethan',
        date: '2025-06-13', pages: 50, office: 'OPP', docketId: 'EPA-HQ-OPP-2021-0221', docNumber: '0119',
        workDone: ['qa'], documentType: 'support', format: ['other_media'],
        isLate: 'on_time', notes: 'Large submission review.', qaMetadataErrors: 'No errors.', qaIndexerName: 'Charlie Brown',
    }
];

const reports: Report[] = [
    { id: 'r_legacy', title: 'Legacy Report', data: [{ Status: 'Complete', Date: '2024-07-10', 'Items Archived': 1204 }], category: 'Narrative', previewComponent: 'LegacyReportPreview' },
    { id: 'r_weekly', title: 'Weekly Report', data: [{ Week: '28', ItemsProcessed: 450, Pending: 32 }], category: 'Productivity', previewComponent: 'WeeklyReportPreview' },
    { id: 'r_planner', title: 'Planner Report', data: [{ Task: 'Review Submissions Q3', Due: '2024-07-15', Assignee: 'All' }], category: 'Productivity', previewComponent: 'PlannerReportPreview' },
    { id: 'r_erule', title: 'eRule Report', data: [{ RuleId: 'ER-2024-05', Status: 'Draft', 'Last Update': '2024-07-12' }], category: 'Spreadsheet', previewComponent: 'ERuleReportPreview' },
    { id: 'r_oar_fr', title: 'OAR FR Report', data: [{ FR_Number: 'FR-12345', PublicationDate: '2024-07-20', 'Docket ID': 'EPA-HQ-OAR-2023-0112'}], category: 'Spreadsheet', previewComponent: 'OarFrReportPreview' },
    { id: 'r_oar_tracking', title: 'OAR Tracking', data: [{ OAR_ID: 'OAR-54321', Step: 'Final Review', 'Next Step': 'Publication' }], category: 'Spreadsheet', previewComponent: 'OarTrackingReportPreview' },
    { id: 'r_ow_tracking', title: 'OW Tracking', data: [{ OW_ID: 'OW-98765', Status: 'In Progress', 'Lead': 'D. Prince' }], category: 'Spreadsheet', previewComponent: 'OwTrackingReportPreview' },
];


export const useDatabase = () => {
    return useMemo(() => ({
        getUsers: () => users,
        getUser: (id: string) => users.find(u => u.id === id),
        getDepartments: () => departments,
        getEmployeesByDepartment: (deptId: string) => users.filter(u => u.departmentId === deptId && u.role === 'employee'),
        getEmployee: (employeeId: string) => users.find(u => u.id === employeeId),
        getEmployeeData: (employeeId: string) => employeeData.find(ed => ed.employeeId === employeeId) || { id: `new-${employeeId}`, employeeId, date: '', pages: 0, office: '', docketId: '', docNumber: '', workDone: [], documentType: '', format: [], isLate: 'on_time', notes: '', qaMetadataErrors: '', qaIndexerName: '' },
        getReports: () => reports,
        getReport: (id: string) => reports.find(r => r.id === id),
    }), []);
};