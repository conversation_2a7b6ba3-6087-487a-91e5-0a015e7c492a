
import React, { createContext, useState, useMemo } from 'react';
import type { User, ActiveItem } from '../types';

interface AppContextType {
    currentUser: User | null;
    setCurrentUser: (user: User | null) => void;
    activeItem: ActiveItem | null;
    setActiveItem: (item: ActiveItem | null) => void;
}

export const AppContext = createContext<AppContextType>({
    currentUser: null,
    setCurrentUser: () => {},
    activeItem: null,
    setActiveItem: () => {},
});

export const AppProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [currentUser, setCurrentUser] = useState<User | null>(null);
    const [activeItem, setActiveItem] = useState<ActiveItem | null>(null);

    const contextValue = useMemo(() => ({
        currentUser,
        setCurrentUser,
        activeItem,
        setActiveItem
    }), [currentUser, activeItem]);

    return (
        <AppContext.Provider value={contextValue}>
            {children}
        </AppContext.Provider>
    );
};
