import React, { useContext } from 'react';
import { AppProvider, AppContext } from './context/AppContext';
import LoginScreen from './components/LoginScreen';
import Header from './components/Header';
import FileCabinet from './components/FileCabinet';
import EmployeeView from './components/EmployeeView';
import ReportTable from './components/ReportTable';
import { useDatabase } from './hooks/useDatabase';

const Dashboard: React.FC = () => {
    const { activeItem } = useContext(AppContext);
    const { getEmployee, getReport } = useDatabase();

    const renderContent = () => {
        if (!activeItem) {
            return (
                <div className="flex-1 flex items-center justify-center bg-slate-800/50 rounded-lg">
                    <div className="text-center">
                        <h2 className="text-2xl font-bold text-slate-400">Welcome to IntelliCabinet</h2>
                        <p className="text-slate-500 mt-2">Select an item from the file cabinet to view its details.</p>
                    </div>
                </div>
            );
        }

        if (activeItem.type === 'employee') {
            const employee = getEmployee(activeItem.id);
            if (employee) return <EmployeeView employee={employee} />;
        }

        if (activeItem.type === 'report') {
            const report = getReport(activeItem.id);
            if (report) return <ReportTable report={report} />;
        }

        return null;
    };

    return (
        <div className="h-screen w-screen flex flex-col bg-slate-900 overflow-hidden">
            <Header />
            <main className="flex-1 flex gap-6 p-6 overflow-hidden">
                <FileCabinet />
                <div className="flex-1 flex flex-col min-w-0">
                    {renderContent()}
                </div>
            </main>
        </div>
    );
};


const App: React.FC = () => {
    return (
        <AppProvider>
            <AppContent />
        </AppProvider>
    );
};

const AppContent: React.FC = () => {
    const { currentUser } = useContext(AppContext);
    
    if (!currentUser) {
        return <LoginScreen />;
    }

    return <Dashboard />;
};


export default App;