export type UserRole = 'employee' | 'manager';

export interface User {
  id: string;
  name: string;
  role: UserRole;
  roleLabel: string;
  departmentId: string;
}

export type WorkType = 'metadata' | 'qa';
export type DocType = 'comment' | 'support' | '';
export type FormatType = 'email' | 'fdms' | 'hardcopy' | 'other_media';
export type OfficeType = 'OPP' | 'OCSPP' | 'OW' | '';
export type LateStatus = 'on_time' | 'late';


export interface EmployeeData {
  id: string;
  employeeId: string;
  // Required Fields
  date: string; // YYYY-MM-DD
  pages: number;
  office: OfficeType;
  docketId: string;
  docNumber: string;
  
  // Work/Doc/Format
  workDone: WorkType[];
  documentType: DocType;
  format: FormatType[];
  
  // Optional Fields
  isLate: LateStatus;
  notes: string;
  qaMetadataErrors: string;
  qaIndexerName: string; // Another employee's name/ID
}


export type ReportCategory = 'Spreadsheet' | 'Productivity' | 'Narrative';

export interface Report {
  id: string;
  title: string;
  data: Record<string, any>[];
  category?: ReportCategory;
  previewComponent?: string;
}

export interface Department {
  id: string;
  name: string;
}

export interface ActiveItem {
    type: 'employee' | 'report';
    id: string;
}