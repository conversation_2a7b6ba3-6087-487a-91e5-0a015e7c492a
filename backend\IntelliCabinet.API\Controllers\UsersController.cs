using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using IntelliCabinet.API.Data;
using IntelliCabinet.API.DTOs;
using System.Text.Json;

namespace IntelliCabinet.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class UsersController : ControllerBase
    {
        private readonly IntelliCabinetDbContext _context;

        public UsersController(IntelliCabinetDbContext context)
        {
            _context = context;
        }

        [HttpGet]
        public async Task<ActionResult<IEnumerable<UserDto>>> GetUsers()
        {
            var users = await _context.Users
                .Include(u => u.Department)
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Name = u.Name,
                    Role = u.Role,
                    RoleLabel = u.RoleLabel,
                    DepartmentId = u.DepartmentId,
                    Department = new DepartmentDto
                    {
                        Id = u.Department.Id,
                        Name = u.Department.Name
                    }
                })
                .ToListAsync();

            return Ok(users);
        }

        [HttpGet("{id}")]
        public async Task<ActionResult<UserDto>> GetUser(string id)
        {
            var user = await _context.Users
                .Include(u => u.Department)
                .Where(u => u.Id == id)
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Name = u.Name,
                    Role = u.Role,
                    RoleLabel = u.RoleLabel,
                    DepartmentId = u.DepartmentId,
                    Department = new DepartmentDto
                    {
                        Id = u.Department.Id,
                        Name = u.Department.Name
                    }
                })
                .FirstOrDefaultAsync();

            if (user == null)
            {
                return NotFound();
            }

            return Ok(user);
        }

        [HttpGet("department/{departmentId}")]
        public async Task<ActionResult<IEnumerable<UserDto>>> GetUsersByDepartment(string departmentId)
        {
            var users = await _context.Users
                .Include(u => u.Department)
                .Where(u => u.DepartmentId == departmentId && u.Role == "employee")
                .Select(u => new UserDto
                {
                    Id = u.Id,
                    Name = u.Name,
                    Role = u.Role,
                    RoleLabel = u.RoleLabel,
                    DepartmentId = u.DepartmentId,
                    Department = new DepartmentDto
                    {
                        Id = u.Department.Id,
                        Name = u.Department.Name
                    }
                })
                .ToListAsync();

            return Ok(users);
        }
    }
}
