namespace IntelliCabinet.API.DTOs
{
    public class UserDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Role { get; set; } = string.Empty;
        public string RoleLabel { get; set; } = string.Empty;
        public string DepartmentId { get; set; } = string.Empty;
        public DepartmentDto? Department { get; set; }
    }

    public class DepartmentDto
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
    }

    public class EmployeeDataDto
    {
        public string Id { get; set; } = string.Empty;
        public string EmployeeId { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty; // YYYY-MM-DD format
        public int Pages { get; set; }
        public string Office { get; set; } = string.Empty;
        public string DocketId { get; set; } = string.Empty;
        public string DocNumber { get; set; } = string.Empty;
        public string[] WorkDone { get; set; } = Array.Empty<string>();
        public string DocumentType { get; set; } = string.Empty;
        public string[] Format { get; set; } = Array.Empty<string>();
        public string IsLate { get; set; } = "on_time";
        public string Notes { get; set; } = string.Empty;
        public string QaMetadataErrors { get; set; } = string.Empty;
        public string QaIndexerName { get; set; } = string.Empty;
    }

    public class ReportDto
    {
        public string Id { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public object[] Data { get; set; } = Array.Empty<object>();
        public string? Category { get; set; }
        public string? PreviewComponent { get; set; }
    }

    public class CreateEmployeeDataDto
    {
        public string EmployeeId { get; set; } = string.Empty;
        public string Date { get; set; } = string.Empty;
        public int Pages { get; set; }
        public string Office { get; set; } = string.Empty;
        public string DocketId { get; set; } = string.Empty;
        public string DocNumber { get; set; } = string.Empty;
        public string[] WorkDone { get; set; } = Array.Empty<string>();
        public string DocumentType { get; set; } = string.Empty;
        public string[] Format { get; set; } = Array.Empty<string>();
        public string IsLate { get; set; } = "on_time";
        public string Notes { get; set; } = string.Empty;
        public string QaMetadataErrors { get; set; } = string.Empty;
        public string QaIndexerName { get; set; } = string.Empty;
    }

    public class UpdateEmployeeDataDto
    {
        public string Date { get; set; } = string.Empty;
        public int Pages { get; set; }
        public string Office { get; set; } = string.Empty;
        public string DocketId { get; set; } = string.Empty;
        public string DocNumber { get; set; } = string.Empty;
        public string[] WorkDone { get; set; } = Array.Empty<string>();
        public string DocumentType { get; set; } = string.Empty;
        public string[] Format { get; set; } = Array.Empty<string>();
        public string IsLate { get; set; } = "on_time";
        public string Notes { get; set; } = string.Empty;
        public string QaMetadataErrors { get; set; } = string.Empty;
        public string QaIndexerName { get; set; } = string.Empty;
    }
}
