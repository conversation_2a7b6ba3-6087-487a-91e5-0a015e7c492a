
import React, { useState, useContext } from 'react';
import { AppContext } from '../context/AppContext';
import { ChevronDownIcon, UserIcon, DocumentReportIcon } from './Icons';
import type { ActiveItem } from '../types';

interface DrawerItem {
    id: string;
    label: string;
    type: 'employee' | 'report';
}

interface DrawerProps {
    title: string;
    items: DrawerItem[];
    icon: React.ReactNode;
    startOpen?: boolean;
}

const Drawer: React.FC<DrawerProps> = ({ title, items, icon, startOpen = false }) => {
    const [isOpen, setIsOpen] = useState(startOpen);
    const { activeItem, setActiveItem } = useContext(AppContext);

    const handleSelect = (item: DrawerItem) => {
        const newActiveItem: ActiveItem = { type: item.type, id: item.id };
        setActiveItem(newActiveItem);
    };

    const itemIcons = {
        employee: <UserIcon className="h-4 w-4 text-slate-500" />,
        report: <DocumentReportIcon className="h-4 w-4 text-slate-500" />,
    };

    return (
        <div className="bg-slate-800/70 rounded-lg">
            <button
                onClick={() => setIsOpen(!isOpen)}
                className="w-full flex items-center justify-between p-3 text-left font-semibold text-slate-200 hover:bg-slate-700/50 rounded-lg transition-colors"
            >
                <div className="flex items-center gap-3">
                    <span className="text-blue-400">{icon}</span>
                    <span>{title}</span>
                </div>
                <ChevronDownIcon className={`h-5 w-5 transition-transform duration-300 ${isOpen ? 'rotate-180' : ''}`} />
            </button>
            <div
                className={`grid transition-all duration-300 ease-in-out ${isOpen ? 'grid-rows-[1fr] opacity-100' : 'grid-rows-[0fr] opacity-0'}`}
            >
                <div className="overflow-hidden">
                    <ul className="py-2 px-3 space-y-1">
                        {items.map(item => (
                            <li key={item.id}>
                                <button
                                    onClick={() => handleSelect(item)}
                                    className={`w-full text-left flex items-center gap-3 py-2 px-3 rounded-md transition-colors ${
                                        activeItem?.id === item.id ? 'bg-blue-600/30 text-blue-200' : 'text-slate-400 hover:bg-slate-700/50 hover:text-slate-200'
                                    }`}
                                >
                                    {itemIcons[item.type]}
                                    <span>{item.label}</span>
                                </button>
                            </li>
                        ))}
                    </ul>
                </div>
            </div>
        </div>
    );
};

export default Drawer;
