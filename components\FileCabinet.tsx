import React, { useContext } from 'react';
import { AppContext } from '../context/AppContext';
import { useDatabase } from '../hooks/useDatabase';
import Drawer from './Drawer';
import { FolderIcon, DocumentReportIcon } from './Icons';

const FileCabinet: React.FC = () => {
    const { currentUser } = useContext(AppContext);
    const { getDepartments, getEmployeesByDepartment, getReports } = useDatabase();

    if (!currentUser) {
        return null;
    }

    const renderManagerView = () => {
        const departments = getDepartments();
        const reports = getReports();
        const reportItems = reports.map(r => ({ id: r.id, label: r.title, type: 'report' as const }));

        return (
            <>
                {departments.map(dept => {
                    const employees = getEmployeesByDepartment(dept.id);
                    const employeeItems = employees.map(emp => ({ id: emp.id, label: emp.name, type: 'employee' as const }));
                    if (employeeItems.length === 0) return null;
                    return (
                        <Drawer
                            key={dept.id}
                            title={dept.name}
                            items={employeeItems}
                            icon={<FolderIcon className="h-5 w-5" />}
                        />
                    );
                })}
                <Drawer
                    title="Reports"
                    items={reportItems}
                    icon={<DocumentReportIcon className="h-5 w-5" />}
                />
            </>
        );
    };

    const renderEmployeeView = () => {
        const departments = getDepartments();
        const userDepartment = departments.find(d => d.id === currentUser.departmentId);

        if (!userDepartment) {
            return <p className="text-slate-400 p-2">No department found for user.</p>;
        }
        
        const employeeItems = [{ id: currentUser.id, label: currentUser.name, type: 'employee' as const }];

        return (
            <Drawer 
                key={userDepartment.id} 
                title={userDepartment.name} 
                items={employeeItems} 
                icon={<FolderIcon className="h-5 w-5" />} 
                startOpen={true} 
            />
        );
    };

    return (
        <aside className="w-80 h-full bg-slate-800/50 rounded-lg p-4 flex flex-col gap-4 shadow-lg border border-slate-700/50">
            <h2 className="text-lg font-semibold text-slate-300 px-2">File Cabinet</h2>
            <div className="flex-1 overflow-y-auto space-y-2 pr-2">
                {currentUser.role === 'manager' ? renderManagerView() : renderEmployeeView()}
            </div>
        </aside>
    );
};

export default FileCabinet;