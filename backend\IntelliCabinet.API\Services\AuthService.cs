using System.Security.Claims;

namespace IntelliCabinet.API.Services
{
    public class AuthService : IAuthService
    {
        public Task<bool> IsUserAuthorizedAsync(ClaimsPrincipal user, string resource, string action)
        {
            // Basic authorization logic - can be extended based on requirements
            if (!user.Identity?.IsAuthenticated ?? true)
            {
                return Task.FromResult(false);
            }

            // For now, all authenticated users can access all resources
            // This can be extended to implement role-based or permission-based authorization
            return Task.FromResult(true);
        }

        public string? GetUserIdFromClaims(ClaimsPrincipal user)
        {
            return user.FindFirst(ClaimTypes.NameIdentifier)?.Value 
                ?? user.FindFirst("sub")?.Value;
        }

        public string? GetUserEmailFromClaims(ClaimsPrincipal user)
        {
            return user.FindFirst(ClaimTypes.Email)?.Value 
                ?? user.FindFirst("email")?.Value;
        }

        public bool IsUserInRole(ClaimsPrincipal user, string role)
        {
            return user.IsInRole(role) 
                || user.HasClaim("roles", role)
                || user.HasClaim(ClaimTypes.Role, role);
        }
    }
}
