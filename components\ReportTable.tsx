
import React from 'react';
import type { Report } from '../types';

interface ReportTableProps {
    report: Report;
}

const ReportTable: React.FC<ReportTableProps> = ({ report }) => {
    if (!report.data || report.data.length === 0) {
        return (
            <div className="flex-1 bg-slate-800/50 rounded-lg p-6 border border-slate-700/50">
                <h2 className="text-2xl font-bold text-white">{report.title}</h2>
                <p className="mt-4 text-slate-400">No data available for this report.</p>
            </div>
        );
    }

    const headers = Object.keys(report.data[0]);

    return (
        <div className="flex-1 bg-slate-800/50 rounded-lg p-6 flex flex-col overflow-hidden border border-slate-700/50">
            <h2 className="text-2xl font-bold text-white mb-4">{report.title}</h2>
            <div className="flex-1 overflow-auto rounded-lg border border-slate-700">
                <table className="w-full text-sm text-left text-slate-300">
                    <thead className="text-xs text-slate-300 uppercase bg-slate-800 sticky top-0">
                        <tr>
                            {headers.map(header => (
                                <th key={header} scope="col" className="px-6 py-3">
                                    {header}
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-slate-900/50">
                        {report.data.map((row, rowIndex) => (
                            <tr key={rowIndex} className="border-b border-slate-700 hover:bg-slate-800/40 transition-colors">
                                {headers.map(header => (
                                    <td key={`${rowIndex}-${header}`} className="px-6 py-4">
                                        {row[header]}
                                    </td>
                                ))}
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
        </div>
    );
};

export default ReportTable;
