import React, { useState, useEffect } from 'react';
import { useDatabase } from '../hooks/useDatabase';
import type { User, EmployeeData, WorkType, DocType, FormatType, OfficeType, LateStatus } from '../types';

interface EmployeeViewProps {
    employee: User;
}

const FormField: React.FC<{ label: string; children: React.ReactNode; className?: string }> = ({ label, children, className }) => (
    <div className={className}>
        <label className="block text-sm font-medium text-slate-300 mb-1">{label}</label>
        {children}
    </div>
);

const CheckboxGroup: React.FC<{ title: string; options: { value: string, label: string }[]; selected: string[]; onChange: (value: string) => void }> = ({ title, options, selected, onChange }) => (
    <div>
        <h3 className="text-sm font-medium text-slate-300 mb-2">{title}</h3>
        <div className="space-y-1">
            {options.map(opt => (
                <label key={opt.value} className="flex items-center gap-2 text-slate-300 cursor-pointer">
                    <input type="checkbox"
                        value={opt.value}
                        checked={selected.includes(opt.value)}
                        onChange={() => onChange(opt.value)}
                        className="h-4 w-4 rounded bg-slate-700 border-slate-600 text-blue-500 focus:ring-blue-500"
                    />
                    {opt.label}
                </label>
            ))}
        </div>
    </div>
);


const EmployeeView: React.FC<EmployeeViewProps> = ({ employee }) => {
    const { getEmployeeData, getUsers } = useDatabase();
    const [formData, setFormData] = useState<EmployeeData>(getEmployeeData(employee.id));

    useEffect(() => {
        setFormData(getEmployeeData(employee.id));
    }, [employee.id, getEmployeeData]);

    const handleInputChange = (field: keyof EmployeeData, value: string | number) => {
        setFormData({ ...formData, [field]: value });
    };

    const handleCheckboxChange = (field: 'workDone' | 'format', value: WorkType | FormatType) => {
        const currentValues = formData[field] as string[];
        const newValues = currentValues.includes(value)
            ? currentValues.filter(item => item !== value)
            : [...currentValues, value];
        setFormData({ ...formData, [field]: newValues });
    };

    const handleRadioChange = (field: 'documentType' | 'isLate', value: DocType | LateStatus) => {
        setFormData({ ...formData, [field]: value });
    };
    
    const handleSave = () => {
        // In a real app, this would be an API call
        console.log("Saving data for", employee.name, ":", formData);
        alert(`Data for ${employee.name} saved to console.`);
    };
    
    const otherUsers = getUsers().filter(u => u.id !== employee.id);

    return (
        <div className="flex-1 bg-slate-800/50 rounded-lg p-6 overflow-y-auto space-y-6 border border-slate-700/50">
            <div className="flex justify-between items-center bg-slate-900/50 p-4 rounded-lg -m-2 mb-4">
                 <div>
                    <h2 className="text-xl font-bold text-white">MetaData/QA Data Entry</h2>
                    <p className="text-slate-400">{employee.name}</p>
                 </div>
                 <button 
                    onClick={handleSave}
                    className="bg-blue-600 hover:bg-blue-500 text-white font-bold py-2 px-4 rounded-lg transition-colors"
                >
                    Save Changes
                </button>
            </div>
            
            <div className="bg-slate-800/60 rounded-lg p-6 border border-slate-700 space-y-4">
                <h3 className="text-lg font-semibold text-slate-200 -mt-2 mb-4">Required Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <FormField label="Date">
                        <input type="date" value={formData.date} onChange={(e) => handleInputChange('date', e.target.value)} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none" />
                    </FormField>
                    <FormField label="# of Pages">
                        <input type="number" value={formData.pages} onChange={(e) => handleInputChange('pages', parseInt(e.target.value, 10) || 0)} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none" />
                    </FormField>
                     <FormField label="Office">
                        <select value={formData.office} onChange={(e) => handleInputChange('office', e.target.value as OfficeType)} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                            <option value="">-- Select --</option>
                            <option value="OPP">OPP</option>
                            <option value="OCSPP">OCSPP</option>
                            <option value="OW">OW</option>
                        </select>
                    </FormField>
                    <FormField label="Docket ID" className="md:col-span-2">
                        <input type="text" value={formData.docketId} onChange={(e) => handleInputChange('docketId', e.target.value)} placeholder="e.g., EPA-HQ-OPP-2021-0221" className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none" />
                    </FormField>
                    <FormField label="Doc #">
                        <input type="text" value={formData.docNumber} onChange={(e) => handleInputChange('docNumber', e.target.value)} placeholder="e.g., 0123" className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none" />
                    </FormField>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 pt-4">
                    <CheckboxGroup title="Work Done" options={[{value: 'metadata', label: 'Metadata'}, {value: 'qa', label: 'QA'}]} selected={formData.workDone} onChange={(v) => handleCheckboxChange('workDone', v as WorkType)} />
                    <div>
                        <h3 className="text-sm font-medium text-slate-300 mb-2">Document Type</h3>
                        <div className="space-y-1">
                            <label className="flex items-center gap-2 text-slate-300 cursor-pointer"><input type="radio" name="docType" value="comment" checked={formData.documentType === 'comment'} onChange={() => handleRadioChange('documentType', 'comment')} className="h-4 w-4 bg-slate-700 border-slate-600 text-blue-500 focus:ring-blue-500" />Comment</label>
                            <label className="flex items-center gap-2 text-slate-300 cursor-pointer"><input type="radio" name="docType" value="support" checked={formData.documentType === 'support'} onChange={() => handleRadioChange('documentType', 'support')} className="h-4 w-4 bg-slate-700 border-slate-600 text-blue-500 focus:ring-blue-500" />Support</label>
                        </div>
                    </div>
                     <CheckboxGroup title="Format" options={[{value: 'email', label: 'Email'}, {value: 'fdms', label: 'FDMS'}, {value: 'hardcopy', label: 'Hardcopy'}, {value: 'other_media', label: 'Other Media'}]} selected={formData.format} onChange={(v) => handleCheckboxChange('format', v as FormatType)} />
                </div>
            </div>

            <div className="bg-slate-800/60 rounded-lg p-6 border border-slate-700 space-y-4">
                <h3 className="text-lg font-semibold text-slate-200 -mt-2 mb-4">Optional Fields</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                     <div>
                        <h3 className="text-sm font-medium text-slate-300 mb-2">Is Document a Late Comment?</h3>
                        <div className="flex gap-4">
                           <label className="flex items-center gap-2 text-slate-300 cursor-pointer"><input type="radio" name="isLate" value="on_time" checked={formData.isLate === 'on_time'} onChange={() => handleRadioChange('isLate', 'on_time')} className="h-4 w-4 bg-slate-700 border-slate-600 text-blue-500 focus:ring-blue-500" />On Time</label>
                           <label className="flex items-center gap-2 text-slate-300 cursor-pointer"><input type="radio" name="isLate" value="late" checked={formData.isLate === 'late'} onChange={() => handleRadioChange('isLate', 'late')} className="h-4 w-4 bg-slate-700 border-slate-600 text-blue-500 focus:ring-blue-500" />Late</label>
                        </div>
                    </div>
                    <FormField label="QA: Name of Indexer">
                         <select value={formData.qaIndexerName} onChange={(e) => handleInputChange('qaIndexerName', e.target.value)} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none">
                             <option value="">-- Select --</option>
                             {otherUsers.map(u => <option key={u.id} value={u.name}>{u.name}</option>)}
                         </select>
                     </FormField>
                    <FormField label="Notes" className="md:col-span-2">
                        <textarea value={formData.notes} onChange={(e) => handleInputChange('notes', e.target.value)} rows={3} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"></textarea>
                    </FormField>
                     <FormField label="QA: Metadata Errors" className="md:col-span-2">
                         <textarea value={formData.qaMetadataErrors} onChange={(e) => handleInputChange('qaMetadataErrors', e.target.value)} rows={2} className="w-full bg-slate-700 border border-slate-600 rounded-md p-2 text-slate-200 focus:ring-2 focus:ring-blue-500 focus:outline-none"></textarea>
                     </FormField>
                </div>
            </div>
        </div>
    );
};

export default EmployeeView;