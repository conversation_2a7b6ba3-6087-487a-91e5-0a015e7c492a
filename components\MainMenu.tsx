
import React from 'react';
import type { User } from '../types';

interface MainMenuProps {
    user: User;
    onNavigate: (view: 'main' | 'monthly_reports') => void;
    onLogout: () => void;
}

const MenuItem: React.FC<{ label: string; onClick: () => void; disabled?: boolean }> = ({ label, onClick, disabled }) => (
    <div className="flex items-center gap-3">
        <button 
            onClick={onClick} 
            className="w-5 h-5 border-2 border-black bg-white shadow-inner disabled:opacity-50" 
            aria-label={label}
            disabled={disabled}
        ></button>
        <span className={`font-semibold text-black ${disabled ? 'text-gray-500' : ''}`}>{label}</span>
    </div>
);

const MainMenu: React.FC<MainMenuProps> = ({ user, onNavigate, onLogout }) => {
    const comingSoon = () => alert(`This feature is not yet implemented.`);

    return (
        <div className="bg-stone-200 p-6 border-2 border-t-white border-l-white border-r-black border-b-black w-full max-w-2xl shadow-lg relative"
             style={{ backgroundImage: "url(\"data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3z' id='path2' fill='%23a2a2a2' fill-opacity='0.2' fill-rule='evenodd'/%3E%3C/svg%3E\")" }}
        >
             <div className="bg-stone-300 absolute inset-0 opacity-40 z-0"></div>
             <div className="relative z-10">

                <div className="text-center mb-6">
                    <h2 className="text-xl md:text-2xl font-bold bg-orange-200 border-2 border-t-black border-l-black border-r-gray-500 border-b-gray-500 inline-block px-6 py-1 shadow-md">MetaData/QA Database</h2>
                    <p className="text-lg font-semibold mt-2 text-black">{user.name}</p>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-x-12 gap-y-3 max-w-lg mx-auto mt-8">
                    <MenuItem label="Data Entry" onClick={comingSoon} disabled />
                    <MenuItem label="Monthly Reports" onClick={() => onNavigate('monthly_reports')} />
                    
                    <MenuItem label="Timecard" onClick={comingSoon} disabled />
                    <MenuItem label="Docket Report" onClick={comingSoon} disabled />

                    <MenuItem label="Staff Report" onClick={comingSoon} disabled />
                    <MenuItem label="Timecard Report" onClick={comingSoon} disabled />

                    <MenuItem label="My Profile" onClick={comingSoon} disabled />
                    <MenuItem label="Metrics Calculator" onClick={comingSoon} disabled />

                    <MenuItem label="Phone List" onClick={comingSoon} disabled />
                    <MenuItem label="Database Admin" onClick={comingSoon} disabled />

                    <MenuItem label="Logout" onClick={onLogout} />
                </div>

                <div className="text-center mt-8">
                    <p className="font-bold text-blue-800/80">EPA/DC <span className="text-sm font-normal text-gray-700/80">DOCKET CENTER</span></p>
                </div>
             </div>
        </div>
    );
};

export default MainMenu;