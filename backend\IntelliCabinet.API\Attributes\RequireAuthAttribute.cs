using Microsoft.AspNetCore.Authorization;

namespace IntelliCabinet.API.Attributes
{
    /// <summary>
    /// Attribute to require authentication for API endpoints
    /// </summary>
    public class RequireAuthAttribute : AuthorizeAttribute
    {
        public RequireAuthAttribute()
        {
            // Default authorization policy
        }

        public RequireAuthAttribute(string policy) : base(policy)
        {
            // Custom authorization policy
        }
    }

    /// <summary>
    /// Attribute to require specific roles for API endpoints
    /// </summary>
    public class RequireRoleAttribute : AuthorizeAttribute
    {
        public RequireRoleAttribute(params string[] roles)
        {
            Roles = string.Join(",", roles);
        }
    }
}
